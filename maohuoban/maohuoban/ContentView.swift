//
//  ContentView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：应用主入口视图 - 遵循MVVM+Coordinator架构，简化为应用入口
//  重构说明：移除复杂逻辑，使用AuthViewModel管理认证状态，LoginView独立处理登录
//

import SwiftUI

// MARK: - 应用主入口视图
struct ContentView: View {

    // MARK: - 状态变量
    /// 是否显示登录弹框
    @State private var isShowingLogin = false

    /// 是否显示登录提示横幅
    @State private var shouldShowLoginPrompt = false

    /// 认证ViewModel
    @StateObject private var authViewModel = AuthViewModel()

    // MARK: - 视图主体
    var body: some View {
        // 🎯 直接显示首页，无论是否登录
        HomeView()
            .overlay(
                // 登录提示横幅（仅在未登录且需要提示时显示）
                loginPromptBanner,
                alignment: .bottom
            )
            .sheet(isPresented: $isShowingLogin) {
                // 登录弹框 - 使用独立的LoginView
                LoginView {
                    // 登录成功回调
                    shouldShowLoginPrompt = false
                    print("✅ 用户登录成功，隐藏登录提示")
                }
            }
            .onAppear {
                checkLoginStatus()
            }
            .onChange(of: authViewModel.isAuthenticated) { isAuthenticated in
                // 监听认证状态变化
                if isAuthenticated {
                    shouldShowLoginPrompt = false
                    print("✅ 认证状态变更为已登录，隐藏登录提示")
                }
            }
    }

    // MARK: - 私有方法

    /// 检查用户登录状态
    private func checkLoginStatus() {
        // 使用AuthViewModel检查登录状态
        authViewModel.checkAuthenticationStatus()

        // 如果未登录，延迟显示登录提示（给用户一些时间浏览）
        if !authViewModel.isAuthenticated {
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                shouldShowLoginPrompt = true
            }
        }

        print("📱 应用启动，用户登录状态：\(authViewModel.isAuthenticated ? "已登录" : "未登录")")
    }

    // MARK: - 子视图

    /// 登录提示横幅
    @ViewBuilder
    private var loginPromptBanner: some View {
        if !authViewModel.isAuthenticated && shouldShowLoginPrompt {
            VStack(spacing: 0) {
                Spacer()

                // 登录提示横幅
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("登录后体验更多功能")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text("发布动态、互动交流、个性化推荐")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    Spacer()

                    Button(action: {
                        isShowingLogin = true
                        print("🔐 用户点击登录按钮")
                    }) {
                        Text("登录")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.white)
                            .cornerRadius(20)
                    }

                    Button(action: {
                        shouldShowLoginPrompt = false
                        print("❌ 用户关闭登录提示")
                    }) {
                        Image(systemName: "xmark")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                            .padding(8)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 0.0, green: 0.48, blue: 0.8),
                            Color(red: 0.0, green: 0.4, blue: 0.7)
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
                .padding(.horizontal, 16)
                .padding(.bottom, 50) // 避免被底部导航栏遮挡
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .animation(.easeInOut(duration: 0.3), value: shouldShowLoginPrompt)
            }
        }
    }
}

// MARK: - 预览
#Preview {
    ContentView()
}
