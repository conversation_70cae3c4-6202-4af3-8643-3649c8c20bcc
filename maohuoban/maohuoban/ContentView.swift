//
//  ContentView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：应用主入口视图 - 遵循MVVM+Coordinator架构，简化为应用入口
//  重构说明：移除复杂逻辑，使用AuthViewModel管理认证状态，LoginView独立处理登录
//

import SwiftUI

// MARK: - 应用主入口视图
struct ContentView: View {

    // MARK: - 状态变量
    /// 是否显示登录提示横幅
    @State private var shouldShowLoginPrompt = false

    /// 认证ViewModel
    @StateObject private var authViewModel = AuthViewModel()

    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            // 🎯 直接显示首页，无论是否登录
            HomeView()
                .overlay(
                    // 登录提示横幅（仅在未登录且需要提示时显示）
                    loginPromptBanner,
                    alignment: .bottom
                )
                .navigationBarHidden(true) // 隐藏导航栏，保持首页的完整性
                .onAppear {
                    checkLoginStatus()
                }
                .onChange(of: authViewModel.isAuthenticated) { isAuthenticated in
                    // 监听认证状态变化
                    if isAuthenticated {
                        shouldShowLoginPrompt = false
                        print("✅ 认证状态变更为已登录，隐藏登录提示")
                    }
                }
        }
        .navigationViewStyle(StackNavigationViewStyle()) // 确保在iPad上也使用堆栈导航
    }

    // MARK: - 私有方法

    /// 检查用户登录状态
    private func checkLoginStatus() {
        // 使用AuthViewModel检查登录状态
        authViewModel.checkAuthenticationStatus()

        // 如果未登录，延迟显示登录提示（给用户一些时间浏览）
        if !authViewModel.isAuthenticated {
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                shouldShowLoginPrompt = true
            }
        }

        print("📱 应用启动，用户登录状态：\(authViewModel.isAuthenticated ? "已登录" : "未登录")")
    }

    // MARK: - 子视图

    /// 登录提示横幅
    /// 使用主题系统颜色，支持日间/夜间模式自适应
    @ViewBuilder
    private var loginPromptBanner: some View {
        if !authViewModel.isAuthenticated && shouldShowLoginPrompt {
            VStack(spacing: 0) {
                Spacer()

                // 登录提示横幅 - 整个区域可点击，导航到登录页面
                NavigationLink(destination: LoginView {
                    // 登录成功回调
                    shouldShowLoginPrompt = false
                    print("✅ 用户登录成功，隐藏登录提示")
                }) {
                    HStack(spacing: MHTheme.spacingS) {
                        // 提示文字区域
                        Text("登录发现更多精彩")
                            .font(MHTheme.bodyFont)
                            .foregroundColor(MHTheme.primaryTextColor)

                        Spacer()

                        // "去登录"文字 - 使用主题色
                        Text("去登录")
                            .font(MHTheme.bodyFont)
                            .fontWeight(.medium)
                            .foregroundColor(MHTheme.primaryColor)
                    }
                    .padding(.horizontal, MHTheme.spacingM)
                    .padding(.vertical, MHTheme.spacingM) // 减小垂直间距
                }
                .background(
                    // 白色背景，支持深色模式自适应
                    RoundedRectangle(cornerRadius: MHTheme.cornerRadiusL)
                        .fill(MHTheme.backgroundColor)
                        .overlay(
                            // 边框
                            RoundedRectangle(cornerRadius: MHTheme.cornerRadiusL)
                                .stroke(MHTheme.separatorColor, lineWidth: 1)
                        )
                )
                .padding(.horizontal, MHTheme.spacingM)
                .padding(.bottom, 65) // 避免被底部导航栏遮挡
                .themeShadow("medium") // 使用主题阴影
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .animation(.easeInOut(duration: 0.3), value: shouldShowLoginPrompt)
            }
        }
    }
}

// MARK: - 预览
#Preview {
    ContentView()
}
