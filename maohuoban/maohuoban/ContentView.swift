//
//  ContentView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：应用主入口视图 - 直接显示首页，支持游客模式和登录弹框
//  重构说明：移除欢迎页面，直接进入应用体验，提升用户体验
//

import SwiftUI

struct ContentView: View {
    // MARK: - 状态变量
    /// 是否显示登录弹框
    @State private var isShowingLogin = false

    /// 用户登录状态
    @State private var isLoggedIn = false

    /// 是否显示登录提示
    @State private var shouldShowLoginPrompt = false

    // MARK: - 私有方法

    /// 检查用户登录状态
    /// 使用TokenManager检查真实的登录状态
    private func checkLoginStatus() {
        // 🔧 使用真实的TokenManager检查登录状态
        isLoggedIn = TokenManager.shared.isTokenValid

        // 如果未登录，延迟显示登录提示（给用户一些时间浏览）
        if !isLoggedIn {
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                shouldShowLoginPrompt = true
            }
        }

        print("用户登录状态检查完成：\(isLoggedIn ? "已登录" : "未登录")")
    }

    // MARK: - 视图主体
    var body: some View {
        // 🎯 直接显示首页，无论是否登录
        HomeView()
            .overlay(
                // 登录提示横幅（仅在未登录且需要提示时显示）
                loginPromptBanner,
                alignment: .bottom
            )
            .sheet(isPresented: $isShowingLogin) {
                // 登录弹框
                LoginView(isLoggedIn: $isLoggedIn) {
                    // 登录成功回调
                    shouldShowLoginPrompt = false
                    print("用户登录成功，隐藏登录提示")
                }
            }
            .onAppear {
                checkLoginStatus()
            }
            .onChange(of: isLoggedIn) { newValue in
                // 监听登录状态变化
                if newValue {
                    shouldShowLoginPrompt = false
                    print("登录状态变更为已登录，隐藏登录提示")
                }
            }
    }

    // MARK: - 子视图

    /// 登录提示横幅
    @ViewBuilder
    private var loginPromptBanner: some View {
        if !isLoggedIn && shouldShowLoginPrompt {
            VStack(spacing: 0) {
                Spacer()

                // 登录提示横幅
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("登录后体验更多功能")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text("发布动态、互动交流、个性化推荐")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    Spacer()

                    Button(action: {
                        isShowingLogin = true
                        print("用户点击登录按钮")
                    }) {
                        Text("登录")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.white)
                            .cornerRadius(20)
                    }

                    Button(action: {
                        shouldShowLoginPrompt = false
                        print("用户关闭登录提示")
                    }) {
                        Image(systemName: "xmark")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                            .padding(8)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 0.0, green: 0.48, blue: 0.8),
                            Color(red: 0.0, green: 0.4, blue: 0.7)
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
                .padding(.horizontal, 16)
                .padding(.bottom, 60) // 避免被底部导航栏遮挡
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .animation(.easeInOut(duration: 0.3), value: shouldShowLoginPrompt)
            }
        }
    }
}

// MARK: - 登录视图
struct LoginView: View {
    // MARK: - 状态变量
    @State private var username = ""
    @State private var password = ""
    @State private var isLoading = false
    @Environment(\.presentationMode) var presentationMode
    @Binding var isLoggedIn: Bool

    // MARK: - 回调闭包
    /// 登录成功回调
    private let onLoginSuccess: (() -> Void)?

    // MARK: - 初始化方法
    init(isLoggedIn: Binding<Bool>, onLoginSuccess: (() -> Void)? = nil) {
        self._isLoggedIn = isLoggedIn
        self.onLoginSuccess = onLoginSuccess
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("登录")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                // 用户名输入框
                TextField("用户名", text: $username)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)

                // 密码输入框
                SecureField("密码", text: $password)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)

                // 登录按钮
                Button(action: {
                    // 处理登录逻辑
                    login()
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        }
                        Text(isLoading ? "登录中..." : "登录")
                            .font(.headline)
                    }
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                    .cornerRadius(10)
                    .opacity(isLoading ? 0.7 : 1.0)
                }
                .disabled(isLoading)

                // 注册链接
                NavigationLink(destination: RegisterView(isLoggedIn: $isLoggedIn)) {
                    Text("没有账号？立即注册")
                        .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                }

                Spacer()
            }
            .padding()
            .navigationBarItems(leading: Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "xmark")
                    .foregroundColor(.gray)
            })
        }
    }

    // MARK: - 私有方法

    /// 执行登录操作
    private func login() {
        // 输入验证
        guard !username.isEmpty, !password.isEmpty else {
            print("用户名或密码不能为空")
            return
        }

        isLoading = true
        print("开始登录流程：用户名=\(username)")

        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 🔧 模拟登录成功，保存Token
            TokenManager.shared.saveTokens(
                accessToken: "mock_access_token_\(username)",
                refreshToken: "mock_refresh_token_\(username)",
                expiresIn: 3600
            )

            // 更新登录状态
            isLoggedIn = true
            isLoading = false

            // 调用成功回调
            onLoginSuccess?()

            // 关闭登录弹框
            presentationMode.wrappedValue.dismiss()

            print("登录成功：用户=\(username)")
        }
    }
}

// 注册视图
struct RegisterView: View {
    @State private var username = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @Binding var isLoggedIn: Bool
    @Environment(\.presentationMode) var presentationMode

    init(isLoggedIn: Binding<Bool>) {
        self._isLoggedIn = isLoggedIn
    }

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text("注册")
                .font(.largeTitle)
                .fontWeight(.bold)

            // 用户名输入框
            TextField("用户名", text: $username)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 邮箱输入框
            TextField("邮箱", text: $email)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 密码输入框
            SecureField("密码", text: $password)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 确认密码输入框
            SecureField("确认密码", text: $confirmPassword)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 注册按钮
            Button(action: {
                // 处理注册逻辑
                register()
            }) {
                Text("注册")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                    .cornerRadius(10)
            }

            Spacer()
        }
        .padding()
        .navigationBarTitle("注册", displayMode: .inline)
    }

    // 注册方法
    private func register() {
        // 这里实现注册逻辑
        print("注册: \(username), \(email), \(password)")

        // 验证输入
        if !username.isEmpty && !email.isEmpty && !password.isEmpty && password == confirmPassword {
            // 模拟注册成功
            isLoggedIn = true
            presentationMode.wrappedValue.dismiss()
        }
    }
}

#Preview {
    ContentView()
}
