//
//  HomeView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：应用主容器视图 - 管理底部导航和页面切换
//  重构说明：已完成第一阶段重构 - 组件模块化分离
//  重构进度：✅ 主题系统分离 ✅ 组件模块化 ⏳ MVVM架构 ⏳ Coordinator模式
//
// TODO: 🚨 重要导航问题需要修复
// 1. 首页左右滑动问题：当前在首页左右滑动会切换到底部导航栏的其他选项（社区等）
//    这不符合预期，底部导航栏应该只能点按切换
// 2. 首页内部滑动：首页内应该是滑动切换"关注、发现、附近"这三个标签
// 3. 需要禁用TabView的滑动手势，只保留底部导航栏的点击切换
//

import SwiftUI

// MARK: - 主视图
/// 应用主界面 - 包含底部导航栏和各个功能模块的容器
/// 重构说明：这是应用的主要容器视图，负责管理底部导航和页面切换
/// 后续将拆分为独立的模块和ViewModel
struct HomeView: View {

    // MARK: - 状态变量
    /// 当前选中的标签页索引
    @State private var selectedTab = 0

    /// 底部安全区域高度 - 用于适配不同设备的底部安全区域
    @State private var bottomSafeArea: CGFloat = 0

    // MARK: - 常量
    /// 标签栏基础高度
    private let tabBarBaseHeight: CGFloat = 50

    // MARK: - 视图主体
    var body: some View {
        ZStack(alignment: .bottom) {
            // MARK: 主内容区域 - 使用TabView管理不同页面的切换
            TabView(selection: $selectedTab) {
                // 首页 - 动态信息流页面
                FeedView()
                    .tag(0)

                // 社区 - 热门内容页面
                HotView()
                    .tag(1)

                // 占位视图 - 中间发布按钮对应的空白页面
                // 注意：这里使用透明色作为占位，实际发布功能通过底部按钮触发
                Color.clear
                    .tag(2)

                // 消息 - 聊天和通知页面
                MessageView()
                    .tag(3)

                // 我的 - 个人资料和设置页面
                ProfileView()
                    .tag(4)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never)) // 隐藏系统默认的页面指示器
            .ignoresSafeArea(.keyboard) // 忽略键盘弹出时的安全区域变化
            .safeAreaInset(edge: .bottom) {
                // 为自定义TabBar预留空间，防止内容被遮挡
                Color.clear
                    .frame(height: tabBarBaseHeight)
            }

            // MARK: 自定义底部导航栏容器
            VStack(spacing: 0) {
                // 自定义Tab栏组件
                MHTabBar(selectedTab: $selectedTab)

                // 底部安全区域填充 - 适配iPhone X系列的Home Indicator区域
                Spacer()
                    .frame(maxWidth: .infinity)
                    .frame(height: bottomSafeArea)
                    .background(Color(UIColor.systemBackground))
            }
            .background(Color.clear)
        }
        .ignoresSafeArea(.all, edges: .bottom) // 忽略底部安全区域，让导航栏延伸到屏幕底部
        // 使用背景GeometryReader获取设备安全区域信息
        .background(
            GeometryReader { proxy in
                Color.clear.onAppear {
                    updateSafeAreaInsets()
                }
            }
        )
        .onAppear {
            // 视图出现时更新安全区域信息
            updateSafeAreaInsets()
        }
    }

    // MARK: - 私有方法
    /// 更新底部安全区域高度
    /// 用于获取设备的底部安全区域高度，适配不同型号的iPhone
    /// 主要用于iPhone X系列的Home Indicator区域适配
    private func updateSafeAreaInsets() {
        DispatchQueue.main.async {
            // 获取当前窗口场景和主窗口
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first else {
                // 如果无法获取窗口信息，则设置为0（适用于模拟器或特殊情况）
                bottomSafeArea = 0
                return
            }
            // 获取底部安全区域高度并更新状态
            bottomSafeArea = window.safeAreaInsets.bottom
        }
    }
}

// MARK: - 注意：MHTabBar组件已移至独立文件
// 文件位置：Presentation/Common/Components/MHTabBar.swift

// MARK: - 注意：FeedView组件已移至独立文件
// 文件位置：Presentation/Screens/Feed/FeedView.swift

// MARK: - 注意：MHTopNavigationBar组件已移至独立文件
// 文件位置：Presentation/Common/Components/MHTopNavigationBar.swift

// MARK: - 注意：MHChannelSelector组件暂时移除
// 该组件当前未被使用，如需要可以创建独立文件

// MARK: - 注意：MHWaterfallLayout组件已移至独立文件
// 文件位置：Presentation/Common/Components/MHWaterfallLayout.swift

// MARK: - 注意：MHFeedCard组件已移至独立文件
// 文件位置：Presentation/Common/Components/MHFeedCard.swift

// MARK: - 注意：其他视图组件已移至独立文件
// HotView -> Presentation/Screens/Hot/HotView.swift
// MessageView -> Presentation/Screens/Message/MessageView.swift
// ProfileView -> Presentation/Screens/Profile/ProfileView.swift

// MARK: - 注意：FeedItem数据模型已移至独立文件
// 文件位置：Presentation/Common/Components/MHWaterfallLayout.swift

// MARK: - 注意：圆角扩展已移至MHTabBar.swift文件中
// 避免重复定义，统一在组件文件中管理

// MARK: - 预览
#Preview {
    HomeView()
}

