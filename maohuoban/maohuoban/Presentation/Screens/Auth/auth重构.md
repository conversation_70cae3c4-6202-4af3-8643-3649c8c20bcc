# 认证模块 (Auth Module)

## 📁 文件结构

```
Auth/
├── AuthViewModel.swift     # 认证业务逻辑ViewModel
├── LoginView.swift        # 登录界面
├── RegisterView.swift     # 注册界面
└── README.md             # 本文档
```

## 🏗️ 架构设计

本模块严格遵循 **MVVM+Coordinator** 架构模式：

### ViewModel层
- **AuthViewModel.swift**: 处理认证相关的所有业务逻辑
  - 登录状态管理
  - 登录/注册操作
  - 错误处理
  - 与TokenManager交互

### View层
- **LoginView.swift**: 登录界面UI
- **RegisterView.swift**: 注册界面UI
- 两个View都通过AuthViewModel处理业务逻辑

## 🔄 数据流

```
View (用户操作) 
  ↓
AuthViewModel (业务逻辑处理)
  ↓
TokenManager (Token管理)
  ↓
AuthViewModel (状态更新)
  ↓
View (UI更新)
```

## ✨ 主要特性

### AuthViewModel
- ✅ 响应式状态管理 (使用 @Published)
- ✅ 输入验证 (邮箱格式、密码匹配等)
- ✅ 加载状态管理
- ✅ 错误处理和提示
- ✅ 自动Token管理

### LoginView
- ✅ 现代化UI设计
- ✅ 密码可见性切换
- ✅ 加载状态显示
- ✅ 表单验证
- ✅ 错误提示弹框

### RegisterView
- ✅ 完整的注册表单
- ✅ 实时密码匹配验证
- ✅ 邮箱格式验证
- ✅ 响应式UI状态

## 🔧 使用方法

### 在ContentView中使用
```swift
.sheet(isPresented: $isShowingLogin) {
    LoginView {
        // 登录成功回调
        print("用户登录成功")
    }
}
```

### 直接使用AuthViewModel
```swift
@StateObject private var authViewModel = AuthViewModel()

// 检查登录状态
authViewModel.checkAuthenticationStatus()

// 执行登录
authViewModel.login(username: "user", password: "pass")

// 监听状态变化
.onChange(of: authViewModel.isAuthenticated) { isAuth in
    // 处理状态变化
}
```

## 🎯 重构成果

### 重构前问题
- ❌ 所有逻辑混在ContentView中
- ❌ 违反单一职责原则
- ❌ 难以测试和维护
- ❌ 代码重复

### 重构后优势
- ✅ 清晰的职责分离
- ✅ 遵循MVVM架构
- ✅ 可重用的组件
- ✅ 易于测试
- ✅ 代码简洁易维护

## 🧪 测试建议

### 单元测试
- AuthViewModel的业务逻辑
- 输入验证功能
- 状态管理

### UI测试
- 登录流程
- 注册流程
- 错误处理

## 🔮 未来扩展

- [ ] 添加第三方登录 (微信、QQ等)
- [ ] 添加生物识别登录
- [ ] 添加忘记密码功能
- [ ] 添加手机号登录
- [ ] 集成真实的API接口
