//
//  LoginView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：登录界面 - 用户登录的UI界面
//  架构说明：遵循MVVM模式，通过AuthViewModel处理业务逻辑
//

import SwiftUI

// MARK: - 登录视图
struct LoginView: View {

    // MARK: - 状态变量
    /// 用户名输入
    @State private var username: String = ""

    /// 密码输入
    @State private var password: String = ""

    /// 是否显示密码
    @State private var isPasswordVisible: Bool = false

    /// 关闭弹框
    @Environment(\.presentationMode) var presentationMode

    /// 认证ViewModel
    @StateObject private var authViewModel = AuthViewModel()

    /// 登录成功回调
    private let onLoginSuccess: (() -> Void)?

    // MARK: - 初始化
    init(onLoginSuccess: (() -> Void)? = nil) {
        self.onLoginSuccess = onLoginSuccess
    }

    // MARK: - 视图主体
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 顶部间距
                Spacer(minLength: 40)

                // 标题区域
                titleSection

                // 输入区域
                inputSection

                // 登录按钮
                loginButton

                // 注册链接
                registerLink

                // 底部间距
                Spacer(minLength: 40)
            }
            .padding(.horizontal, 24)
        }
        .navigationBarTitle("登录", displayMode: .inline)
        .alert("登录失败", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("确定") {
                authViewModel.clearError()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
        .onChange(of: authViewModel.isAuthenticated) { isAuthenticated in
            if isAuthenticated {
                onLoginSuccess?()
                // 页面导航模式下，登录成功后自动返回
                presentationMode.wrappedValue.dismiss()
            }
        }
    }

    // MARK: - 子视图

    /// 标题区域
    private var titleSection: some View {
        VStack(spacing: 12) {
            // 应用图标
            Image(systemName: "pawprint.fill")
                .font(.system(size: 60))
                .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8))

            // 标题
            Text("欢迎回来")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            // 副标题
            Text("登录您的毛伙伴账号")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }

    /// 输入区域
    private var inputSection: some View {
        VStack(spacing: 16) {
            // 用户名输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("用户名")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                TextField("请输入用户名", text: $username)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
            }

            // 密码输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                HStack {
                    if isPasswordVisible {
                        TextField("请输入密码", text: $password)
                    } else {
                        SecureField("请输入密码", text: $password)
                    }

                    Button(action: {
                        isPasswordVisible.toggle()
                    }) {
                        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())
            }
        }
    }

    /// 登录按钮
    private var loginButton: some View {
        Button(action: {
            authViewModel.login(username: username, password: password)
        }) {
            HStack {
                if authViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(authViewModel.isLoading ? "登录中..." : "登录")
                    .font(.headline)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(red: 0.0, green: 0.48, blue: 0.8))
                    .opacity(authViewModel.isLoading ? 0.7 : 1.0)
            )
        }
        .disabled(authViewModel.isLoading || username.isEmpty || password.isEmpty)
    }

    /// 注册链接
    private var registerLink: some View {
        NavigationLink(destination: RegisterView(onRegisterSuccess: onLoginSuccess)) {
            HStack {
                Text("还没有账号？")
                    .foregroundColor(.secondary)

                Text("立即注册")
                    .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8))
                    .fontWeight(.medium)
            }
            .font(.subheadline)
        }
    }


}

// MARK: - 预览
#Preview {
    LoginView()
}
