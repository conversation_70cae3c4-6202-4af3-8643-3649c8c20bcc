//
//  LoginView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：登录界面 - 用户登录的UI界面
//  架构说明：遵循MVVM模式，通过AuthViewModel处理业务逻辑
//

import SwiftUI

// MARK: - 登录页面
struct LoginView: View {

    // MARK: - 状态变量
    /// 手机号输入
    @State private var phoneNumber: String = ""

    /// 验证码输入
    @State private var verificationCode: String = ""

    /// 是否已发送验证码
    @State private var isCodeSent: Bool = false

    /// 验证码倒计时
    @State private var countdown: Int = 0

    /// 倒计时定时器
    @State private var timer: Timer?

    /// 是否同意协议
    @State private var isAgreedToTerms: Bool = false

    /// 关闭弹框
    @Environment(\.presentationMode) var presentationMode

    /// 认证ViewModel
    @StateObject private var authViewModel = AuthViewModel()

    /// 登录成功回调
    private let onLoginSuccess: (() -> Void)?

    // MARK: - 初始化
    init(onLoginSuccess: (() -> Void)? = nil) {
        self.onLoginSuccess = onLoginSuccess
    }

    // MARK: - 视图主体
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 顶部区域
                VStack(spacing: 32) {
                    // 标题区域
                    titleSection

                    // 副标题
                    subtitleSection

                    // 输入区域
                    inputSection

                    // 登录按钮（仅在验证码发送后显示）
                    if isCodeSent {
                        loginButton
                    }

                    // 协议同意区域
                    termsSection
                }
                .padding(.horizontal, 32)
                .padding(.top, 60)

                Spacer()

                // TODO: 第三方登录功能 - 后续需要时实现
                // thirdPartyLoginSection
                //     .padding(.bottom, geometry.safeAreaInsets.bottom + 40)
            }
        }
        .background(MHTheme.backgroundColor.ignoresSafeArea())
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarItems(trailing: helpButton)
        .contentShape(Rectangle()) // 确保整个区域都可以响应点击
        .onTapGesture {
            // 点击空白处关闭软键盘
            hideKeyboard()
        }
        .alert("登录失败", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("确定") {
                authViewModel.clearError()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
        .onChange(of: authViewModel.isAuthenticated) { isAuthenticated in
            if isAuthenticated {
                onLoginSuccess?()
                presentationMode.wrappedValue.dismiss()
            }
        }
        .onDisappear {
            // 清理定时器
            timer?.invalidate()
            timer = nil
        }
    }

    // MARK: - 子视图

    /// 标题区域
    private var titleSection: some View {
        Text("手机号登录")
            .font(.system(size: 24, weight: .medium))
            .foregroundColor(MHTheme.primaryTextColor)
    }

    /// 副标题区域
    private var subtitleSection: some View {
        Text("未注册的手机号登录后将自动注册")
            .font(.system(size: 14))
            .foregroundColor(MHTheme.secondaryTextColor)
    }

    /// 帮助按钮
    private var helpButton: some View {
        Button("帮助") {
            // 帮助功能
            print("显示帮助信息")
        }
        .font(.system(size: 16))
        .foregroundColor(MHTheme.secondaryTextColor)
    }

    /// 输入区域 - 渐进式显示
    private var inputSection: some View {
        VStack(spacing: 24) {
            // 手机号输入区域
            phoneNumberSection

            // 验证码输入区域（仅在发送验证码后显示）
            if isCodeSent {
                verificationCodeSection
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .animation(.easeInOut(duration: 0.3), value: isCodeSent)
            }
        }
    }

    /// 手机号输入区域
    private var phoneNumberSection: some View {
        VStack(spacing: 16) {
            // 国家代码和手机号输入
            HStack(spacing: 12) {
                // 国家代码选择器 - 下划线样式
                VStack(alignment: .leading, spacing: 8) {
                    Button(action: {
                        // 国家代码选择功能
                        print("选择国家代码")
                    }) {
                        HStack(spacing: 4) {
                            Text("+86")
                                .font(.system(size: 16))
                                .foregroundColor(MHTheme.primaryTextColor)

                            Image(systemName: "chevron.down")
                                .font(.system(size: 12))
                                .foregroundColor(MHTheme.secondaryTextColor)
                        }
                    }

                    // 下划线
                    Rectangle()
                        .fill(MHTheme.separatorColor)
                        .frame(height: 1)
                }
                .frame(width: 60)

                // 手机号输入框 - 下划线样式
                VStack(alignment: .leading, spacing: 8) {
                    TextField("请输入手机号", text: $phoneNumber)
                        .font(.system(size: 16))
                        .keyboardType(.phonePad)
                        .foregroundColor(MHTheme.primaryTextColor)
                        .onChange(of: phoneNumber) { newValue in
                            // 限制手机号长度为11位，并自动发送验证码
                            if newValue.count > 11 {
                                phoneNumber = String(newValue.prefix(11))
                            }

                            // 当输入11位手机号时自动发送验证码
                            if newValue.count == 11 && newValue.hasPrefix("1") && !isCodeSent {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    sendVerificationCode()
                                }
                            }
                        }

                    // 下划线
                    Rectangle()
                        .fill(phoneNumber.isEmpty ? MHTheme.separatorColor : MHTheme.primaryColor)
                        .frame(height: 1)
                        .animation(.easeInOut(duration: 0.2), value: phoneNumber.isEmpty)
                }
            }

            // 密码登录切换
            HStack {
                Image(systemName: "lock")
                    .font(.system(size: 14))
                    .foregroundColor(MHTheme.primaryColor)

                Button("密码登录") {
                    // 切换到密码登录
                    print("切换到密码登录")
                }
                .font(.system(size: 14))
                .foregroundColor(MHTheme.primaryColor)

                Spacer()

                Text("号码不可用")
                    .font(.system(size: 14))
                    .foregroundColor(MHTheme.tertiaryTextColor)
            }
        }
    }

    /// 验证码输入区域 - 下划线样式
    private var verificationCodeSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            TextField("请输入验证码", text: $verificationCode)
                .font(.system(size: 16))
                .keyboardType(.numberPad)
                .foregroundColor(MHTheme.primaryTextColor)
                .onChange(of: verificationCode) { newValue in
                    // 限制验证码长度为6位
                    if newValue.count > 6 {
                        verificationCode = String(newValue.prefix(6))
                    }
                }

            // 下划线
            Rectangle()
                .fill(verificationCode.isEmpty ? MHTheme.separatorColor : MHTheme.primaryColor)
                .frame(height: 1)
                .animation(.easeInOut(duration: 0.2), value: verificationCode.isEmpty)
        }
    }

    /// 登录按钮
    private var loginButton: some View {
        Button(action: {
            authViewModel.login(username: phoneNumber, password: verificationCode)
        }) {
            HStack {
                if authViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(authViewModel.isLoading ? "登录中..." : "验证并登录")
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(MHTheme.primaryColor)
                    .opacity(authViewModel.isLoading || !isFormValid ? 0.5 : 1.0)
            )
        }
        .disabled(authViewModel.isLoading || !isFormValid)
    }

    /// 协议同意区域
    private var termsSection: some View {
        HStack(alignment: .top, spacing: 8) {
            Button(action: {
                isAgreedToTerms.toggle()
            }) {
                Image(systemName: isAgreedToTerms ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 16))
                    .foregroundColor(isAgreedToTerms ? MHTheme.primaryColor : MHTheme.tertiaryTextColor)
            }

            VStack(alignment: .leading, spacing: 0) {
                Text("我已阅读并同意")
                    .font(.system(size: 12))
                    .foregroundColor(MHTheme.tertiaryTextColor)

                HStack(spacing: 4) {
                    Button("《用户协议》") {
                        // 显示用户协议
                        showUserAgreement()
                    }
                    .font(.system(size: 12))
                    .foregroundColor(MHTheme.primaryColor)

                    Button("《隐私政策》") {
                        // 显示隐私政策
                        showPrivacyPolicy()
                    }
                    .font(.system(size: 12))
                    .foregroundColor(MHTheme.primaryColor)

                    Button("《未成年人个人信息保护规则》") {
                        // 显示未成年人保护规则
                        showMinorProtectionRules()
                    }
                    .font(.system(size: 12))
                    .foregroundColor(MHTheme.primaryColor)
                }
            }

            Spacer()
        }
    }

    // TODO: 第三方登录区域 - 后续需要时实现
    /*
    /// 第三方登录区域
    private var thirdPartyLoginSection: some View {
        HStack(spacing: 40) {
            // 微信登录
            Button(action: {
                print("微信登录")
            }) {
                Circle()
                    .fill(Color.green)
                    .frame(width: 44, height: 44)
                    .overlay(
                        Text("微")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                    )
            }

            // QQ登录
            Button(action: {
                print("QQ登录")
            }) {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 44, height: 44)
                    .overlay(
                        Text("Q")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                    )
            }

            // Apple登录
            Button(action: {
                print("Apple登录")
            }) {
                Circle()
                    .fill(MHTheme.primaryTextColor)
                    .frame(width: 44, height: 44)
                    .overlay(
                        Image(systemName: "apple.logo")
                            .font(.system(size: 24))
                            .foregroundColor(MHTheme.backgroundColor)
                    )
            }
        }
    }
    */

    // MARK: - 计算属性

    /// 手机号是否有效
    private var isValidPhoneNumber: Bool {
        phoneNumber.count == 11 && phoneNumber.hasPrefix("1")
    }

    /// 表单是否有效
    private var isFormValid: Bool {
        isValidPhoneNumber &&
        !verificationCode.isEmpty &&
        verificationCode.count == 6 &&
        isAgreedToTerms // 必须同意协议
    }

    // MARK: - 私有方法

    /// 发送验证码
    private func sendVerificationCode() {
        guard isValidPhoneNumber else {
            print("❌ 手机号格式不正确")
            return
        }

        // 🔧 模拟发送验证码
        print("📱 发送验证码到：\(phoneNumber)")
        isCodeSent = true
        countdown = 60

        // 启动倒计时
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if countdown > 0 {
                countdown -= 1
            } else {
                timer?.invalidate()
                timer = nil
            }
        }
    }

    /// 关闭软键盘
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    /// 显示用户协议
    private func showUserAgreement() {
        print("📄 显示用户协议")
        // TODO: 实现用户协议页面导航或弹框
        // 可以使用 NavigationLink 或 sheet 来显示协议内容
    }

    /// 显示隐私政策
    private func showPrivacyPolicy() {
        print("🔒 显示隐私政策")
        // TODO: 实现隐私政策页面导航或弹框
    }

    /// 显示未成年人保护规则
    private func showMinorProtectionRules() {
        print("👶 显示未成年人个人信息保护规则")
        // TODO: 实现未成年人保护规则页面导航或弹框
    }
}

// MARK: - 预览
#Preview {
    LoginView()
}
