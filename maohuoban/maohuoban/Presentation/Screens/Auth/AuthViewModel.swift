//
//  AuthViewModel.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：认证模块的ViewModel - 处理登录、注册等认证相关的业务逻辑
//  架构说明：遵循MVVM模式，负责状态管理和业务逻辑处理
//

import Foundation
import Combine

// MARK: - 认证状态枚举
/// 认证状态
enum AuthState {
    case idle           // 空闲状态
    case loading        // 加载中
    case authenticated  // 已认证
    case unauthenticated // 未认证
    case error(String)  // 错误状态
}

// MARK: - 认证ViewModel
/// 认证模块的ViewModel
/// 负责处理登录、注册、登出等认证相关的业务逻辑
class AuthViewModel: ObservableObject {
    
    // MARK: - 发布属性
    /// 认证状态
    @Published var authState: AuthState = .idle
    
    /// 是否正在加载
    @Published var isLoading: Bool = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// 是否已登录
    @Published var isAuthenticated: Bool = false
    
    // MARK: - 私有属性
    /// Combine订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init() {
        checkAuthenticationStatus()
        setupBindings()
    }
    
    // MARK: - 公共方法
    
    /// 检查认证状态
    func checkAuthenticationStatus() {
        let isValid = TokenManager.shared.isTokenValid
        isAuthenticated = isValid
        authState = isValid ? .authenticated : .unauthenticated
        
        print("🔐 认证状态检查：\(isValid ? "已登录" : "未登录")")
    }
    
    /// 登录
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    func login(username: String, password: String) {
        // 输入验证
        guard !username.isEmpty, !password.isEmpty else {
            setError("用户名和密码不能为空")
            return
        }
        
        setLoading(true)
        print("🔐 开始登录：用户名=\(username)")
        
        // 模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.performLogin(username: username, password: password)
        }
    }
    
    /// 注册
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - password: 密码
    ///   - confirmPassword: 确认密码
    func register(username: String, email: String, password: String, confirmPassword: String) {
        // 输入验证
        guard !username.isEmpty, !email.isEmpty, !password.isEmpty else {
            setError("所有字段都不能为空")
            return
        }
        
        guard password == confirmPassword else {
            setError("两次输入的密码不一致")
            return
        }
        
        guard isValidEmail(email) else {
            setError("邮箱格式不正确")
            return
        }
        
        setLoading(true)
        print("🔐 开始注册：用户名=\(username), 邮箱=\(email)")
        
        // 模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
            self?.performRegister(username: username, email: email, password: password)
        }
    }
    
    /// 登出
    func logout() {
        print("🔐 用户登出")
        
        // 清除Token
        TokenManager.shared.clearTokens()
        
        // 更新状态
        isAuthenticated = false
        authState = .unauthenticated
        clearError()
    }
    
    /// 清除错误信息
    func clearError() {
        errorMessage = nil
        if case .error = authState {
            authState = isAuthenticated ? .authenticated : .unauthenticated
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置加载状态
    private func setLoading(_ loading: Bool) {
        isLoading = loading
        if loading {
            authState = .loading
            clearError()
        }
    }
    
    /// 设置错误信息
    private func setError(_ message: String) {
        errorMessage = message
        authState = .error(message)
        isLoading = false
        print("❌ 认证错误：\(message)")
    }
    
    /// 执行登录操作
    private func performLogin(username: String, password: String) {
        // 🔧 模拟登录成功，保存Token
        TokenManager.shared.saveTokens(
            accessToken: "mock_access_token_\(username)",
            refreshToken: "mock_refresh_token_\(username)",
            expiresIn: 3600
        )
        
        // 更新状态
        isAuthenticated = true
        authState = .authenticated
        isLoading = false
        clearError()
        
        print("✅ 登录成功：用户=\(username)")
    }
    
    /// 执行注册操作
    private func performRegister(username: String, email: String, password: String) {
        // 🔧 模拟注册成功，自动登录
        TokenManager.shared.saveTokens(
            accessToken: "mock_access_token_\(username)",
            refreshToken: "mock_refresh_token_\(username)",
            expiresIn: 3600
        )
        
        // 更新状态
        isAuthenticated = true
        authState = .authenticated
        isLoading = false
        clearError()
        
        print("✅ 注册成功：用户=\(username), 邮箱=\(email)")
    }
    
    /// 设置数据绑定
    private func setupBindings() {
        // 监听认证状态变化
        $authState
            .sink { [weak self] state in
                switch state {
                case .authenticated:
                    self?.isAuthenticated = true
                case .unauthenticated, .error:
                    self?.isAuthenticated = false
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }
    
    /// 验证邮箱格式
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}
