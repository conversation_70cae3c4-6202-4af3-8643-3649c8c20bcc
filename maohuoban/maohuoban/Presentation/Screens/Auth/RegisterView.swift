//
//  RegisterView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：注册界面 - 用户注册的UI界面
//  架构说明：遵循MVVM模式，通过AuthViewModel处理业务逻辑
//

import SwiftUI

// MARK: - 注册视图
struct RegisterView: View {

    // MARK: - 状态变量
    /// 手机号输入
    @State private var phoneNumber: String = ""

    /// 验证码输入
    @State private var verificationCode: String = ""

    /// 用户名输入
    @State private var username: String = ""

    /// 密码输入
    @State private var password: String = ""

    /// 确认密码输入
    @State private var confirmPassword: String = ""

    /// 是否已发送验证码
    @State private var isCodeSent: Bool = false

    /// 验证码倒计时
    @State private var countdown: Int = 0

    /// 倒计时定时器
    @State private var timer: Timer?

    /// 是否显示密码
    @State private var isPasswordVisible: Bool = false

    /// 是否显示确认密码
    @State private var isConfirmPasswordVisible: Bool = false

    /// 关闭弹框
    @Environment(\.presentationMode) var presentationMode

    /// 认证ViewModel
    @StateObject private var authViewModel = AuthViewModel()

    /// 注册成功回调
    private let onRegisterSuccess: (() -> Void)?

    // MARK: - 初始化
    init(onRegisterSuccess: (() -> Void)? = nil) {
        self.onRegisterSuccess = onRegisterSuccess
    }

    // MARK: - 视图主体
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 顶部间距
                Spacer(minLength: 20)

                // 标题区域
                titleSection

                // 输入区域
                inputSection

                // 注册按钮
                registerButton

                // 底部间距
                Spacer(minLength: 40)
            }
            .padding(.horizontal, 24)
        }
        .navigationBarTitle("注册", displayMode: .inline)
        .alert("注册失败", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("确定") {
                authViewModel.clearError()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
        .onChange(of: authViewModel.isAuthenticated) { isAuthenticated in
            if isAuthenticated {
                onRegisterSuccess?()
                presentationMode.wrappedValue.dismiss()
            }
        }
    }

    // MARK: - 子视图

    /// 标题区域
    private var titleSection: some View {
        VStack(spacing: 12) {
            // 应用图标
            Image(systemName: "pawprint.fill")
                .font(.system(size: 50))
                .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8))

            // 标题
            Text("加入毛伙伴")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            // 副标题
            Text("创建您的账号，开始宠物社交之旅")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    /// 输入区域
    private var inputSection: some View {
        VStack(spacing: 16) {
            // 手机号输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("手机号")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                TextField("请输入手机号", text: $phoneNumber)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.phonePad)
                    .onChange(of: phoneNumber) { newValue in
                        // 限制手机号长度为11位
                        if newValue.count > 11 {
                            phoneNumber = String(newValue.prefix(11))
                        }
                    }
            }

            // 验证码输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("验证码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                HStack {
                    TextField("请输入验证码", text: $verificationCode)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.numberPad)
                        .onChange(of: verificationCode) { newValue in
                            // 限制验证码长度为6位
                            if newValue.count > 6 {
                                verificationCode = String(newValue.prefix(6))
                            }
                        }

                    // 发送验证码按钮
                    Button(action: sendVerificationCode) {
                        Text(countdown > 0 ? "\(countdown)s" : "发送验证码")
                            .font(.subheadline)
                            .foregroundColor(countdown > 0 ? .secondary : .white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(countdown > 0 ? Color.gray.opacity(0.3) : Color(red: 0.0, green: 0.48, blue: 0.8))
                            )
                    }
                    .disabled(countdown > 0 || !isValidPhoneNumber)
                }
            }

            // 用户名输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("用户名")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                TextField("请输入用户名", text: $username)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
            }

            // 密码输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                HStack {
                    if isPasswordVisible {
                        TextField("请输入密码", text: $password)
                    } else {
                        SecureField("请输入密码", text: $password)
                    }

                    Button(action: {
                        isPasswordVisible.toggle()
                    }) {
                        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())
            }

            // 确认密码输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("确认密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                HStack {
                    if isConfirmPasswordVisible {
                        TextField("请再次输入密码", text: $confirmPassword)
                    } else {
                        SecureField("请再次输入密码", text: $confirmPassword)
                    }

                    Button(action: {
                        isConfirmPasswordVisible.toggle()
                    }) {
                        Image(systemName: isConfirmPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())

                // 密码匹配提示
                if !confirmPassword.isEmpty && password != confirmPassword {
                    Text("两次输入的密码不一致")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
        }
    }

    /// 注册按钮
    private var registerButton: some View {
        Button(action: {
            authViewModel.register(
                username: username,
                email: phoneNumber, // 使用手机号作为邮箱字段
                password: password,
                confirmPassword: confirmPassword
            )
        }) {
            HStack {
                if authViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(authViewModel.isLoading ? "注册中..." : "注册")
                    .font(.headline)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(red: 0.0, green: 0.48, blue: 0.8))
                    .opacity(authViewModel.isLoading ? 0.7 : 1.0)
            )
        }
        .disabled(authViewModel.isLoading || !isFormValid)
    }

    // MARK: - 计算属性

    /// 手机号是否有效
    private var isValidPhoneNumber: Bool {
        phoneNumber.count == 11 && phoneNumber.hasPrefix("1")
    }

    /// 表单是否有效
    private var isFormValid: Bool {
        isValidPhoneNumber &&
        !verificationCode.isEmpty &&
        verificationCode.count == 6 &&
        !username.isEmpty &&
        !password.isEmpty &&
        !confirmPassword.isEmpty &&
        password == confirmPassword
    }

    // MARK: - 私有方法

    /// 发送验证码
    private func sendVerificationCode() {
        guard isValidPhoneNumber else {
            print("❌ 手机号格式不正确")
            return
        }

        // 🔧 模拟发送验证码
        print("📱 发送验证码到：\(phoneNumber)")
        isCodeSent = true
        countdown = 60

        // 启动倒计时
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if countdown > 0 {
                countdown -= 1
            } else {
                timer?.invalidate()
                timer = nil
            }
        }
    }
}

// MARK: - 预览
#Preview {
    NavigationView {
        RegisterView()
    }
}
