//
//  RegisterView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：注册界面 - 用户注册的UI界面
//  架构说明：遵循MVVM模式，通过AuthViewModel处理业务逻辑
//

import SwiftUI

// MARK: - 注册视图
struct RegisterView: View {
    
    // MARK: - 状态变量
    /// 用户名输入
    @State private var username: String = ""
    
    /// 邮箱输入
    @State private var email: String = ""
    
    /// 密码输入
    @State private var password: String = ""
    
    /// 确认密码输入
    @State private var confirmPassword: String = ""
    
    /// 是否显示密码
    @State private var isPasswordVisible: Bool = false
    
    /// 是否显示确认密码
    @State private var isConfirmPasswordVisible: Bool = false
    
    /// 关闭弹框
    @Environment(\.presentationMode) var presentationMode
    
    /// 认证ViewModel
    @StateObject private var authViewModel = AuthViewModel()
    
    /// 注册成功回调
    private let onRegisterSuccess: (() -> Void)?
    
    // MARK: - 初始化
    init(onRegisterSuccess: (() -> Void)? = nil) {
        self.onRegisterSuccess = onRegisterSuccess
    }
    
    // MARK: - 视图主体
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 顶部间距
                Spacer(minLength: 20)
                
                // 标题区域
                titleSection
                
                // 输入区域
                inputSection
                
                // 注册按钮
                registerButton
                
                // 底部间距
                Spacer(minLength: 40)
            }
            .padding(.horizontal, 24)
        }
        .navigationBarTitle("注册", displayMode: .inline)
        .alert("注册失败", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("确定") {
                authViewModel.clearError()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
        .onChange(of: authViewModel.isAuthenticated) { isAuthenticated in
            if isAuthenticated {
                onRegisterSuccess?()
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 标题区域
    private var titleSection: some View {
        VStack(spacing: 12) {
            // 应用图标
            Image(systemName: "pawprint.fill")
                .font(.system(size: 50))
                .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8))
            
            // 标题
            Text("加入毛伙伴")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            // 副标题
            Text("创建您的账号，开始宠物社交之旅")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    /// 输入区域
    private var inputSection: some View {
        VStack(spacing: 16) {
            // 用户名输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("用户名")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                TextField("请输入用户名", text: $username)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
            }
            
            // 邮箱输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("邮箱")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                TextField("请输入邮箱", text: $email)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
            }
            
            // 密码输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                HStack {
                    if isPasswordVisible {
                        TextField("请输入密码", text: $password)
                    } else {
                        SecureField("请输入密码", text: $password)
                    }
                    
                    Button(action: {
                        isPasswordVisible.toggle()
                    }) {
                        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            // 确认密码输入框
            VStack(alignment: .leading, spacing: 8) {
                Text("确认密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                HStack {
                    if isConfirmPasswordVisible {
                        TextField("请再次输入密码", text: $confirmPassword)
                    } else {
                        SecureField("请再次输入密码", text: $confirmPassword)
                    }
                    
                    Button(action: {
                        isConfirmPasswordVisible.toggle()
                    }) {
                        Image(systemName: isConfirmPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())
                
                // 密码匹配提示
                if !confirmPassword.isEmpty && password != confirmPassword {
                    Text("两次输入的密码不一致")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
        }
    }
    
    /// 注册按钮
    private var registerButton: some View {
        Button(action: {
            authViewModel.register(
                username: username,
                email: email,
                password: password,
                confirmPassword: confirmPassword
            )
        }) {
            HStack {
                if authViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                
                Text(authViewModel.isLoading ? "注册中..." : "注册")
                    .font(.headline)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(red: 0.0, green: 0.48, blue: 0.8))
                    .opacity(authViewModel.isLoading ? 0.7 : 1.0)
            )
        }
        .disabled(authViewModel.isLoading || !isFormValid)
    }
    
    // MARK: - 计算属性
    
    /// 表单是否有效
    private var isFormValid: Bool {
        !username.isEmpty &&
        !email.isEmpty &&
        !password.isEmpty &&
        !confirmPassword.isEmpty &&
        password == confirmPassword
    }
}

// MARK: - 预览
#Preview {
    NavigationView {
        RegisterView()
    }
}
