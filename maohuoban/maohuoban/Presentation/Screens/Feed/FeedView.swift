//
//  FeedView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：首页动态信息流视图
//  重构说明：从HomeView.swift中分离出来，实现Feed模块的独立管理
//  架构模式：MVVM - 这是View层，后续会添加对应的ViewModel
//
// ✅ 首页内部滑动切换功能已实现
// 1. ✅ 已实现"关注、发现、附近"三个标签的滑动切换功能
// 2. ✅ 支持顶部导航栏点击切换和左右滑动手势
// 3. ✅ 使用TabView实现滑动切换，与顶部导航栏状态同步
// 4. ✅ 滑动切换只在首页内部生效，不影响底部导航栏
//

import SwiftUI

// MARK: - 首页动态信息流视图
/// 首页Feed视图 - 显示用户关注的动态、发现内容和附近内容
/// 包含顶部导航栏和瀑布流内容展示
/// 支持关注、发现、附近三个频道的切换
struct FeedView: View {

    // MARK: - 状态变量
    /// 当前选中的频道索引 (0:关注, 1:发现, 2:附近)
    /// 默认选中"发现"频道
    @State private var selectedChannel = 1

    /// 搜索文本 - 用于顶部搜索功能
    @State private var searchText = ""

    // MARK: - 常量
    /// 主标签选项数组 - 包含关注、发现、附近三个频道
    private let subChannels = ["关注", "发现", "附近"]

    // MARK: - 视图主体
    var body: some View {
        VStack(spacing: 0) {
            // MARK: 顶部导航区域
            MHTopNavigationBar(
                searchText: $searchText,
                selectedMainTab: $selectedChannel,
                mainTabs: subChannels,
                showLocationIcon: false // 不单独显示位置图标，位置信息集成在"附近"频道中
            )

            // MARK: 内容区域 - 支持滑动切换的标签页
            // 🔧 新增：使用TabView实现"关注、发现、附近"三个标签的滑动切换
            TabView(selection: $selectedChannel) {
                // 关注频道内容
                createChannelContent(for: 0, title: "关注")
                    .tag(0)

                // 发现频道内容
                createChannelContent(for: 1, title: "发现")
                    .tag(1)

                // 附近频道内容
                createChannelContent(for: 2, title: "附近")
                    .tag(2)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never)) // 隐藏页面指示器
            .onChange(of: selectedChannel) { newValue in
                // 当滑动切换标签时，添加触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()

                // 日志记录
                print("滑动切换到频道：\(subChannels[newValue])")
            }
        }
        .navigationBarHidden(true) // 隐藏系统导航栏，使用自定义导航栏
        .onAppear {
            // 视图出现时的初始化操作
            setupFeedView()
        }
    }

    // MARK: - 私有方法

    /// 创建频道内容视图
    /// - Parameters:
    ///   - index: 频道索引
    ///   - title: 频道标题
    /// - Returns: 频道内容视图
    private func createChannelContent(for index: Int, title: String) -> some View {
        ScrollView {
            // 根据不同频道显示不同内容
            VStack(spacing: 8) {
                // 频道标识（开发阶段用于区分不同频道）
                if index != 1 { // 发现频道不显示标识，保持原有样式
                    Text("📍 \(title)频道内容")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.top, 8)
                }

                // 瀑布流内容展示
                // 🔧 注意：这里复用了原有的瀑布流组件
                // 后续可以根据不同频道加载不同的数据源
                MHWaterfallLayout()
                    .padding(.top, index == 1 ? 8 : 0) // 发现频道保持原有间距
            }
        }
        .refreshable {
            // 下拉刷新功能
            await refreshFeedData(for: index)
        }
    }

    /// 刷新指定频道的Feed数据
    /// - Parameter channelIndex: 频道索引
    private func refreshFeedData(for channelIndex: Int = -1) async {
        // TODO: 实现数据刷新逻辑
        // 这里将来会调用API获取最新的Feed数据

        let channelName = channelIndex >= 0 ? subChannels[channelIndex] : "当前频道"

        // 模拟网络请求延迟
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟

        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        print("\(channelName)数据刷新完成")
    }

    /// 设置Feed视图
    /// 在视图出现时进行必要的初始化操作
    private func setupFeedView() {
        // TODO: 初始化Feed数据
        // 这里将来会调用ViewModel的初始化方法

        print("FeedView初始化完成，当前频道：\(subChannels[selectedChannel])")
    }
}

// MARK: - 预览
#Preview {
    FeedView()
}
