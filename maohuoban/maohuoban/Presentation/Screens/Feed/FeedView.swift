//
//  FeedView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：首页动态信息流视图
//  重构说明：从HomeView.swift中分离出来，实现Feed模块的独立管理
//  架构模式：MVVM - 这是View层，后续会添加对应的ViewModel
//
// TODO: 🔄 首页内部滑动切换功能
// 1. 需要实现"关注、发现、附近"三个标签的滑动切换功能
// 2. 当前只有顶部导航栏的点击切换，需要添加左右滑动手势
// 3. 可以使用TabView或自定义手势识别来实现
// 4. 确保滑动切换只在首页内部生效，不影响底部导航栏
//

import SwiftUI

// MARK: - 首页动态信息流视图
/// 首页Feed视图 - 显示用户关注的动态、发现内容和附近内容
/// 包含顶部导航栏和瀑布流内容展示
/// 支持关注、发现、附近三个频道的切换
struct FeedView: View {

    // MARK: - 状态变量
    /// 当前选中的频道索引 (0:关注, 1:发现, 2:附近)
    /// 默认选中"发现"频道
    @State private var selectedChannel = 1

    /// 搜索文本 - 用于顶部搜索功能
    @State private var searchText = ""

    // MARK: - 常量
    /// 主标签选项数组 - 包含关注、发现、附近三个频道
    private let subChannels = ["关注", "发现", "附近"]

    // MARK: - 视图主体
    var body: some View {
        VStack(spacing: 0) {
            // MARK: 顶部导航区域
            MHTopNavigationBar(
                searchText: $searchText,
                selectedMainTab: $selectedChannel,
                mainTabs: subChannels,
                showLocationIcon: false // 不单独显示位置图标，位置信息集成在"附近"频道中
            )

            // MARK: 内容区域 - 瀑布流布局
            ScrollView {
                // 瀑布流内容展示
                MHWaterfallLayout()
                    .padding(.top, 8) // 顶部留出一些间距
            }
            .refreshable {
                // TODO: 实现下拉刷新功能
                // 这里将来会调用ViewModel的刷新方法
                await refreshFeedData()
            }
        }
        .navigationBarHidden(true) // 隐藏系统导航栏，使用自定义导航栏
        .onAppear {
            // 视图出现时的初始化操作
            setupFeedView()
        }
    }

    // MARK: - 私有方法

    /// 刷新Feed数据
    /// 异步方法，用于下拉刷新时重新加载数据
    private func refreshFeedData() async {
        // TODO: 实现数据刷新逻辑
        // 这里将来会调用API获取最新的Feed数据

        // 模拟网络请求延迟
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟

        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        print("Feed数据刷新完成")
    }

    /// 设置Feed视图
    /// 在视图出现时进行必要的初始化操作
    private func setupFeedView() {
        // TODO: 初始化Feed数据
        // 这里将来会调用ViewModel的初始化方法

        print("FeedView初始化完成，当前频道：\(subChannels[selectedChannel])")
    }
}

// MARK: - 预览
#Preview {
    FeedView()
}
